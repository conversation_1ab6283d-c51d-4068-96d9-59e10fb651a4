import com.codingame.gameengine.runner.MultiplayerGameRunner;

public class Main {
    public static void main(String[] args) {

        // int LEAGUE = 5;

        // MultiplayerGameRunner gameRunner = new MultiplayerGameRunner();
        // gameRunner.setLeagueLevel(LEAGUE);

        // Set seed here (leave commented for random)
        // gameRunner.setSeed(-1566415677164768800L);

        // Select agents here

		CustomGameRunner gameRunner = new CustomGameRunner();
		gameRunner.setLeagueLevel(5);
		gameRunner.addAgent("/home/<USER>/progra/codingame/GameEngine/bot", "Bot A");
		gameRunner.addAgent("/home/<USER>/progra/codingame/GameEngine/bot", "Bot B");

		gameRunner.runWithoutGUI();
		System.exit(0);

        // gameRunner.addAgent("/home/<USER>/progra/codingame/GameEngine/bot", "RAZOG");
        // gameRunner.addAgent("/home/<USER>/progra/codingame/GameEngine/bot", "RAZOG2");

        // gameRunner.start();
    }
}
