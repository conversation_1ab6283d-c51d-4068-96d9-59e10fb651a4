{"name": "view", "version": "1.0.0", "description": "", "main": "config.js", "dependencies": {"pixi.js": "^5.0.0"}, "devDependencies": {"@types/pixi.js": "^5.0.0", "@typescript-eslint/eslint-plugin": "2.28.0", "@typescript-eslint/parser": "2.28.0", "eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-config-standard-jsx": "8.1.0", "eslint-loader": "3.0.4", "eslint-plugin-import": "2.20.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "2.5.1", "eslint-plugin-standard": "4.0.1", "typescript": "3.8.3"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "tsc -w"}, "author": "", "license": "ISC"}